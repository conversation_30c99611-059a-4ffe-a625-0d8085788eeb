import DButton from '@/components/Global/DButton';
import useDanteApi from '@/hooks/useDanteApi';
import { getVoiceConversations } from '@/services/voice.service';
import { useEffect, useState, useRef } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { DateTime } from 'luxon';
import DBadge from '@/components/Global/DBadge';
import { STATUS } from '@/constants';
import DSwitch from '@/components/Global/DSwitch';
import useLayoutStore from '@/stores/layout/layoutStore';
import DLoading from '@/components/DLoading';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';

const VoiceConversations = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
    const switchRef = useRef(null);

    // Responsive breakpoints
    const isAboveSm = useIsAboveBreakpoint('sm');
    const isAboveMd = useIsAboveBreakpoint('md');

    // Check if we're coming from the onboarding popup
    const searchParams = new URLSearchParams(location.search);
    const fromOnboarding = searchParams.get('source') === 'onboarding-popup';

    const { data: conversations, isLoading, error, refetch } = useDanteApi(getVoiceConversations, [], { }, params.id);

    const [tableData, setTableData] = useState([]);
    const [liveConversations, setLiveConversations] = useState(fromOnboarding);




    // Table columns - responsive widths for mobile
    const tableColumns = [
        {
            label: 'Caller Number',
            key: 'caller_number',
            showName: true,
            minWidth: isAboveMd ? 'min-w-32' : 'min-w-20',
            mobile: true,
        },
        {
            label: 'Date started',
            key: 'date_started',
            showName: true,
            minWidth: isAboveMd ? 'min-w-36' : 'min-w-24',
            mobile: true,
        },
        {
            label: 'Date ended',
            key: 'date_ended',
            showName: true,
            minWidth: isAboveMd ? 'min-w-36' : 'min-w-24',
            mobile: true,
        },
        {
            label: 'Status',
            key: 'status',
            showName: true,
            minWidth: isAboveMd ? 'min-w-24' : 'min-w-16',
            mobile: true,
        },
        {
            label: 'Duration',
            key: 'duration',
            showName: true,
            minWidth: isAboveMd ? 'min-w-20' : 'min-w-16',
            mobile: true,
        },
        {
            label: 'Actions',
            key: 'actions',
            showName: true,
            minWidth: isAboveMd ? 'min-w-24' : 'min-w-16',
            mobile: false,
        }
    ];

    useEffect(() => {
        if (conversations?.results?.length > 0) {
            setTableData(conversations?.results?.map((item) => ({
                caller_number: item.caller_number,
                date_started: item.call_started_at ? DateTime.fromISO(item.call_started_at).toLocaleString(DateTime.DATETIME_SHORT) : '',
                date_ended: item.call_ended_at ? DateTime.fromISO(item.call_ended_at).toLocaleString(DateTime.DATETIME_SHORT) : '',
                status: <DBadge
                    label={item.status.replace(/_/g, ' ').toUpperCase()}
                    type={item.status === 'done' ? STATUS.SUCCESS : STATUS.WORKING}
                    showIcon={false}
                />,
                duration: item?.duration ? `${Math.floor(item.duration / 60)}m ${item.duration % 60}s` : '0m 0s',
                actions: <DButton variant="outlined" size="sm" onClick={() => navigate(`${item.id}`)}>
                    View
                </DButton>,
            })));
        }
    }, [conversations]);

    useEffect(() => {
        if (liveConversations) {
            const interval = setInterval(() => {
                refetch();
            }, 3000);
            return () => clearInterval(interval);
        }
    }, [liveConversations]);

    useEffect(() => {
        setSidebarOpen(false);
    }, []);

    // Add glow effect if coming from onboarding
    useEffect(() => {
        if (fromOnboarding && switchRef.current) {
            // Clear the source parameter from the URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('source');
            window.history.replaceState({}, '', newUrl.toString());
        }
    }, [fromOnboarding]);

    // if(isLoading) {
    //     return <DLoading show={true} />
    // }

    return (
        <div className={`flex flex-col bg-white rounded-size1 h-[1px] grow overflow-y-auto  ${
            isAboveSm ? 'gap-size5 p-size5 no-scrollbar' : 'gap-size3 p-size3 scrollbar'
        } ${isAboveMd ? 'p-size6' : ''}`}>
            <div className="flex items-center justify-between w-full">
                {/* Title - show on larger screens */}
                {isAboveMd && (
                    <h1 className="text-xl font-medium text-grey-400">AI Voice Conversations</h1>
                )}

                {/* Live conversations toggle */}
                <div
                    ref={switchRef}
                    className={`${isAboveMd ? 'self-end' : 'ml-auto'} ${
                        fromOnboarding
                            ? `animate-pulse shadow-md shadow-purple-20 rounded-lg ${isAboveSm ? 'p-2' : 'p-1'}`
                            : ''
                    }`}
                >
                    <DSwitch
                        label="Enable live conversations"
                        checked={liveConversations}
                        onChange={(checked) => setLiveConversations(checked)}
                    />
                </div>
            </div>
            <div className="flex flex-col gap-size1 sm:gap-size2 w-full h-full">
                {isLoading ? (
                    <div className="flex items-center justify-center w-full h-full py-size7">
                        <DLoading show={true} />
                    </div>
                ) : tableData && tableData.length > 0 ? (
                    <div className="overflow-x-auto overflow-y-auto scrollbar rounded-size1 border border-grey-5 bg-white shadow-sm max-h-[calc(100vh-280px)] min-h-[120px]">
                        {isAboveSm ? (
                            // Desktop Table Layout
                            <div className={isAboveMd ? "min-w-[700px]" : "min-w-[500px]"}>
                                <table className={`w-full table-auto text-left ${isAboveMd ? 'text-base' : 'text-sm'}`}>
                                    <thead className="bg-grey-5">
                                        <tr>
                                            {tableColumns.map((column) => (
                                                <th
                                                    className={`px-size1 py-size2 text-lg font-regular tracking-tight ${
                                                        column.minWidth ? column.minWidth : 'min-w-10'
                                                    }`}
                                                    key={column.key}
                                                >
                                                    {column.showName ? column.label : ''}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {tableData.map((row, index) => (
                                            <tr key={index} className="h-14 border-b border-grey-5 last:border-b-0">
                                                {tableColumns.map((column) => (
                                                    <td
                                                        className={`px-size1 py-size2 ${
                                                            column.minWidth ? column.minWidth : 'min-w-10'
                                                        }`}
                                                        key={column.key}
                                                    >
                                                        <div className="flex items-center gap-size1">
                                                            {row[column.key]}
                                                        </div>
                                                    </td>
                                                ))}
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            // Mobile Card Layout
                            <div className="p-size2">
                                {tableData.map((row, index) => (
                                    <div key={index} className="bg-white border border-grey-5 rounded-size1 mb-size3 last:mb-0 shadow-sm">
                                        {/* Card Header with Actions */}
                                        <div className="bg-grey-5 border-b border-grey-5 p-size3 flex items-center justify-between">
                                            <div className="flex flex-col gap-size1">
                                                <span className="text-sm font-medium tracking-tight text-grey-400">
                                                    {row.caller_number}
                                                </span>
                                                <div className="flex items-center gap-size2">
                                                    {row.status}
                                                </div>
                                            </div>
                                            {row.actions}
                                        </div>

                                        {/* Card Content */}
                                        <div className="p-size3">
                                            <div className="grid grid-cols-2 gap-size3">
                                                {tableColumns.map((column) =>
                                                    column.mobile && column.key !== 'caller_number' && column.key !== 'status' && column.key !== 'actions' && (
                                                        <div key={column.key} className="flex flex-col gap-size1">
                                                            <span className="text-xs font-medium tracking-tight text-grey-50">
                                                                {column.label}
                                                            </span>
                                                            <span className="text-sm font-regular tracking-tight">
                                                                {row[column.key]}
                                                            </span>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="flex flex-col items-center justify-center w-full h-full py-size5 sm:py-size7 px-size3 sm:px-size5">
                        <div className="text-center">
                            <div className="w-16 h-16 mx-auto mb-size3 bg-grey-5 rounded-full flex items-center justify-center">
                                <svg className="w-8 h-8 text-grey-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <p className="text-base sm:text-lg text-grey-50 text-center mb-size1">
                                No conversations found
                            </p>
                
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default VoiceConversations;